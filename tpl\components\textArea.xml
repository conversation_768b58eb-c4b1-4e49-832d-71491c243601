{php if(!isset($GLOBALS["textAreaCtr"])) $GLOBALS["textAreaCtr"] = 10;}
{var $textAreaId = ($post ?? NULL) === NULL ? (++$GLOBALS["textAreaCtr"]) : $post->getId()}
{var $textAreaId = ($custom_id ?? NULL) === NULL ? $textAreaId : $custom_id}
{var $anonEnabled = OPENVK_ROOT_CONF['openvk']['preferences']['wall']['anonymousPosting']['enable']}
{var $anonAccountId = OPENVK_ROOT_CONF['openvk']['preferences']['wall']['anonymousPosting']['account'] ?? 100}
{var $anonUser = $anonEnabled ? (new \openvk\Web\Models\Repositories\Users())->get($anonAccountId) : NULL}

<div id="write" class='model_content_textarea'>
    <form action="{$route}" method="post" enctype="multipart/form-data" style="margin:0;">
    {if $thisUser ?? NULL}
        <a class="post_field_user_link" href="{$thisUser->getURL()}">
            <img class="post_field_user_image"
                    src="{if str_contains($thisUser->getAvatarUrl('miniscule'), 'camera_200.png')}/themepack/modern_mobile/{$theme->getVersion()}/resource/camera_200.png{else}{$thisUser->getAvatarUrl('miniscule')}{/if}"
                    alt="{$thisUser->getCanonicalName()}"
                    width="28" height="28"
                    data-user-url="{$thisUser->getURL()}" />
            {if !is_null($club ?? NULL)}
            <img class="post_field_user_image post_field_user_image_group"
                    src="{if str_contains($club->getAvatarUrl('miniscule'), 'camera_200.png')}/themepack/modern_mobile/{$theme->getVersion()}/resource/community_200.png{else}{$club->getAvatarUrl('miniscule')}{/if}"
                    alt="{$club->getCanonicalName()}"
                    width="28" height="28"
                    style="opacity: 0;"
                    data-group-url="{$club->getURL()}" />
            {/if}
            {if $anonEnabled && ($postOpts ?? true) && $anonUser}
            <img class="post_field_user_image post_field_user_image_anon"
                    src="{$anonUser->getAvatarUrl('miniscule')}"
                    alt="{$anonUser->getCanonicalName()}"
                    width="28" height="28"
                    style="opacity: 0;"
                    data-anon-url="{$anonUser->getURL()}" />
            {/if}
        </a>
    {/if}
        <textarea id="wall-post-input{$textAreaId}" placeholder="{_write}" name="text"
            style="resize: none;" class="small-textarea"></textarea>
        <div id="post-buttons{$textAreaId}" class='post-buttons'>
            <div class="post-horizontal"></div>
            <div class="post-vertical"></div>
            <div class="post-has-poll">
                {_poll}
            </div>
            <div class="post-has-geo"></div>
            <div class="post-source"></div>

            <input type="hidden" name="horizontal_attachments" value="" autocomplete="off" />
            <input type="hidden" name="vertical_attachments" value="" autocomplete="off" />
            <input type="hidden" name="poll" value="none" autocomplete="off" />
            <input type="hidden" id="source" name="source" value="none" autocomplete="off" />
            <input type="hidden" name="geo" value="" autocomplete="off" />
            <input type="hidden" name="type" value="1" autocomplete="off" />
            <input type="hidden" name="hash" value="{$csrfToken}" />
            <div class="post-bottom-acts">
                <div class="post-attach-menu">
                    <div id="wallAttachmentMenu">
                        <a id="__photoAttachment" class="attach_photo" data-tip="simple-black" data-align="bottom-center" data-title="{_photo}"
                           {if !is_null($club ?? NULL) && $club->canBeModifiedBy($thisUser)}data-club="{$club->getId()}"{/if}>
                            <div class="post-attach-menu__icon"></div>
                        </a>
                        <a id="__videoAttachment" class="attach_video" data-tip="simple-black" data-align="bottom-center" data-title="{_video}">
                            <div class="post-attach-menu__icon"></div>
                        </a>
                        <a id="__audioAttachment" class="attach_audio" data-tip="simple-black" data-align="bottom-center" data-title="{_audio}">
                            <div class="post-attach-menu__icon"></div>
                        </a>

                        <a class="post-attach-menu__trigger" id="moreAttachTrigger">
                            {_show_more}
                        </a>
                        <div class="tippy-menu" id="moreAttachTooltip">
                            <a n:if="$docs ?? true" id="__documentAttachment" class="attach_document"
                               {if !is_null($club ?? NULL) && $club->canBeModifiedBy($thisUser)}data-club="{$club->getRealId()}"{/if}>
                                <div class="post-attach-menu__icon"></div>
                                {_document}
                            </a>
                            <a n:if="$notes ?? false" id="__notesAttachment" class="attach_note">
                                <div class="post-attach-menu__icon"></div>
                                {_note}
                            </a>
                            <a n:if="$graffiti ?? false" class="attach_graffiti" onclick="window.vkifyGraffiti(event);">
                                <div class="post-attach-menu__icon"></div>
                                {_graffiti}
                            </a>
                            <a n:if="$polls ?? false" class="attach_poll" onclick="initPoll(event);">
                                <div class="post-attach-menu__icon"></div>
                                {_poll}
                            </a>
                            <a n:if="$geo ?? false" id="__geoAttacher" class="attach_geo">
                                <div class="post-attach-menu__icon"></div>
                                {_geo_place}
                            </a>
                        </div>
                    </div>
                </div>
                <div class="post-bottom-buttons">
                    {if $postOpts ?? true}
                        <div class="post_settings" id="postOptsTrigger" role="button">
                            <div class="common_icon"></div>
                        </div>
                
                        <div class="post-opts tippy-menu" id="postOptsTooltip">
                            {if !is_null($thisUser) && !is_null($club ?? NULL) && $owner < 0 && $club->canBeModifiedBy($thisUser)}
                                <label>
                                    <input type="checkbox" name="as_group" onchange="window.onWallAsGroupClick(this)" />
                                    {_post_as_group}
                                </label>
                                <label id="forceSignOpt" style="display: none;">
                                    <input type="checkbox" name="force_sign" /> {_add_signature}
                                </label>
                            {/if}
                
                            <label n:if="$anonEnabled" id="octoberAnonOpt">
                                <input type="checkbox" name="anon" onchange="window.onWallAnonClick(this)" /> {_as_anonymous}
                            </label>
                
                            <label>
                                <input type="checkbox" name="nsfw" /> {_contains_nsfw}
                            </label>
                            {if $hasSource}
                                <div class="separator"></div>
                                <a id="__sourceAttacher" class="attach_source">
                                    <div class="post-attach-menu__icon"></div>
                                    {_set_source}
                                </a>
                            {/if}
                        </div>
                    {/if}
                    {if !($postOpts ?? true) && !is_null($thisUser) && !is_null($club ?? NULL) && $club->canBeModifiedBy($thisUser)}
                        <div class="post_settings" id="postOptsTrigger" role="button">
                            <div class="common_icon"></div>
                        </div>

                        <div class="post-opts" id="postOptsTooltip">
                            <label>
                                <input type="checkbox" name="as_group" onchange="window.onWallAsGroupClick(this)" />
                                {_comment_as_group}
                            </label>
                        </div>
                    {/if}
                     <input type="submit" value="{_write}" class="button" />
                </div>
            </div>
        </div>
    </form>
</div>