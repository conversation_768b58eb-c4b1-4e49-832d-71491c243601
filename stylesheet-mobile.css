/**
 * Mobile Menu Styles for OpenVK Modern Mobile Theme
 * Styles for the hamburger menu modal system
 */

/* Mobile Menu Modal Specific Styles */
.mobile-menu-modal {
    /* Override default modal positioning for mobile */
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 100% !important;
    height: 100% !important;
    z-index: 9999 !important;
}

.mobile-menu-modal .ovk-diag {
    /* Full screen modal for mobile */
    width: 100% !important;
    height: 100% !important;
    max-width: none !important;
    max-height: none !important;
    margin: 0 !important;
    border-radius: 0 !important;
    display: flex;
    flex-direction: column;
}

.mobile-menu-modal .ovk-diag-head {
    /* Header styling */
    background: var(--header-background-color, #4a76a8);
    color: white;
    padding: 15px 20px;
    font-size: 18px;
    font-weight: bold;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    flex-shrink: 0;
}

.mobile-menu-modal .ovk-diag-body {
    /* Body takes remaining space and scrolls */
    flex: 1;
    overflow-y: auto;
    padding: 0;
    background: var(--background-color, #ffffff);
}

.mobile-menu-modal .ovk-diag-action {
    /* Hide action area since we don't use buttons */
    display: none;
}

/* Mobile Menu Content Wrapper */
.mobile-menu-wrapper {
    width: 100%;
    height: 100%;
}

.mobile-menu-wrapper .navigation {
    padding: 20px 0;
    background: transparent;
}

/* Mobile Menu Links */
.mobile-menu-wrapper .navigation a.link {
    display: block;
    padding: 15px 20px;
    color: var(--text-color, #2a5885);
    text-decoration: none;
    border-bottom: 1px solid var(--border-color, #e7e8ec);
    font-size: 16px;
    transition: background-color 0.2s ease;
}

.mobile-menu-wrapper .navigation a.link:hover,
.mobile-menu-wrapper .navigation a.link:active {
    background-color: var(--hover-background-color, #f7f8fa);
    text-decoration: none;
}

.mobile-menu-wrapper .navigation a.link:last-child {
    border-bottom: none;
}

/* Menu Dividers */
.mobile-menu-wrapper .menu_divider {
    height: 1px;
    background: var(--border-color, #e7e8ec);
    margin: 10px 0;
    border: none;
}

/* Notification badges in mobile menu */
.mobile-menu-wrapper .navigation a.link object[type="internal/link"] {
    float: right;
    margin-top: -2px;
}

.mobile-menu-wrapper .navigation a.link object[type="internal/link"] a,
.mobile-menu-wrapper .navigation a.link object[type="internal/link"] b {
    color: var(--accent-color, #5181b8);
    font-weight: bold;
}

/* Group links styling */
.mobile-menu-wrapper .navigation a.group_link {
    padding-left: 30px;
    font-size: 14px;
    color: var(--muted-text-color, #656565);
}

/* News section in mobile menu */
.mobile-menu-wrapper #news {
    padding: 15px 20px;
    background: var(--light-background-color, #f7f8fa);
    margin: 10px 0;
    border-radius: 8px;
    margin-left: 20px;
    margin-right: 20px;
}

.mobile-menu-wrapper #news b {
    color: var(--text-color, #2a5885);
    font-size: 16px;
}

.mobile-menu-wrapper #news hr {
    border: none;
    height: 1px;
    background: var(--border-color, #e7e8ec);
    margin: 10px 0;
}

.mobile-menu-wrapper #news text {
    color: var(--muted-text-color, #656565);
    line-height: 1.4;
}

.mobile-menu-wrapper #news a {
    color: var(--accent-color, #5181b8);
    text-decoration: none;
}

/* Footer links in mobile menu */
.mobile-menu-wrapper .left_menu_nav_wrap {
    padding: 20px;
    background: var(--light-background-color, #f7f8fa);
    margin-top: 20px;
}

.mobile-menu-wrapper .left_menu_nav_wrap a {
    display: block;
    padding: 10px 0;
    color: var(--muted-text-color, #656565);
    text-decoration: none;
    font-size: 14px;
    border-bottom: 1px solid var(--border-color, #e7e8ec);
}

.mobile-menu-wrapper .left_menu_nav_wrap a:last-child {
    border-bottom: none;
}

.mobile-menu-wrapper .left_menu_nav_wrap a:hover {
    color: var(--accent-color, #5181b8);
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .mobile-menu-modal .ovk-diag-head {
        padding: 12px 15px;
        font-size: 16px;
    }
    
    .mobile-menu-wrapper .navigation a.link {
        padding: 12px 15px;
        font-size: 15px;
    }
}

/* Dark theme support (if implemented) */
@media (prefers-color-scheme: dark) {
    .mobile-menu-modal .ovk-diag-head {
        background: #1a1a1a;
        border-bottom-color: rgba(255, 255, 255, 0.1);
    }
    
    .mobile-menu-modal .ovk-diag-body {
        background: #000000;
    }
    
    .mobile-menu-wrapper .navigation a.link {
        color: #ffffff;
        border-bottom-color: #333333;
    }
    
    .mobile-menu-wrapper .navigation a.link:hover {
        background-color: #1a1a1a;
    }
    
    .mobile-menu-wrapper .menu_divider {
        background: #333333;
    }
    
    .mobile-menu-wrapper #news {
        background: #1a1a1a;
    }
    
    .mobile-menu-wrapper .left_menu_nav_wrap {
        background: #1a1a1a;
    }
}
