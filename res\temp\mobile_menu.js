/**
 * Mobile Menu System for OpenVK Modern Mobile Theme
 * Implements a hamburger menu using the existing messagebox system
 */

window.openMenu = function() {
    // Check if navigation exists
    const navigation = document.querySelector('.navigation');
    if (!navigation) {
        console.warn('Navigation element not found');
        return;
    }

    // Clone the navigation content to avoid modifying the original
    const navigationClone = navigation.cloneNode(true);
    
    // Create a wrapper for the mobile menu content
    const menuWrapper = document.createElement('div');
    menuWrapper.className = 'mobile-menu-wrapper';
    menuWrapper.appendChild(navigationClone);

    // Get the HTML content for the modal
    const menuContent = menuWrapper.outerHTML;

    // Create the mobile menu modal using the existing messagebox system
    const mobileMenu = new CMessageBox({
        title: 'Menu', // This could be localized if needed
        body: menuContent,
        buttons: [], // No buttons needed for the menu
        close_on_buttons: false, // Don't close when clicking buttons (since we have none)
        unique_name: 'mobile_navigation_menu',
        custom_template: null
    });

    // Add mobile menu specific class to the modal for styling
    const modalNode = mobileMenu.getNode();
    if (modalNode && modalNode.nodes && modalNode.nodes[0]) {
        modalNode.nodes[0].classList.add('mobile-menu-modal');
    }

    // Handle link clicks in the mobile menu
    modalNode.on('click', 'a.link', function(e) {
        // Allow normal navigation, but close the menu
        mobileMenu.close();
    });

    // Close menu when clicking outside (this is already handled by messagebox.js)
    // but we can add additional mobile-specific behavior if needed

    return mobileMenu;
};

// Optional: Add keyboard shortcut for opening menu (useful for testing)
document.addEventListener('keydown', function(e) {
    // Open menu with Ctrl+M (or Cmd+M on Mac)
    if ((e.ctrlKey || e.metaKey) && e.key === 'm' && e.shiftKey) {
        e.preventDefault();
        window.openMenu();
    }
});

// Optional: Auto-close menu on window resize (mobile orientation change)
window.addEventListener('resize', function() {
    // Close any open mobile menu when screen orientation changes
    const openMenus = window.messagebox_stack.filter(msg => msg.unique_name === 'mobile_navigation_menu');
    openMenus.forEach(menu => menu.close());
});
