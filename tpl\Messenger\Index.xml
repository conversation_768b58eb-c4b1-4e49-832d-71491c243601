{extends "../@layout.xml"}
{block title}{_my_messages}{/block}/block}

{block content}
	{var $sendMessageButton = '<a href="/friends' . $thisUser->getId() . '" class="button">' . tr('send_message') . '</a>'}
	{include "../components/page_block_header.xml", title => "my_messages", count => sizeof($corresps), extra => $sendMessageButton}

    <div class="page_block">

        <div class="scroll_container" style="min-height: 430px;">
		    {if sizeof($corresps) > 0}
                <ul class="im-page--dcontent">
				{foreach $corresps as $coresp}
					{var $lastMsg   = $coresp->getPreviewMessage()}
                    <li
                        n:class="'nim-dialog nim-dialog_classic scroll_node', $lastMsg->getUnreadState() ? 'nim-dialog--unread'"
                        onmousedown="window.open({$coresp->getURL()}, '_blank').focus();">
                        {var $recipient = $coresp->getCorrespondents()[1]}
                        <div class="nim-dialog--photo">
                            <div n:class="nim-peer, $recipient->isOnline() ? online">
                                <div class="nim-peer--photo-w">
                                    <div class="nim-peer--photo _im_dialog_photo">
                                        <a href="{$recipient->getURL()}" target="_blank">
                                            <div class="im_grid">
                                                <img src="{if str_contains($recipient->getAvatarURL('miniscule'), 'camera_200.png')}/themepack/modern_mobile/{$theme->getVersion()}/resource/camera_200.png{else}{$recipient->getAvatarURL('miniscule')}{/if}" width="50" height="50" alt="{_photo}" loading="lazy">
                                            </div>
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="nim-dialog--content"> 
                            <div class="nim-dialog--cw">
                                <div class="nim-dialog--date_wrapper">
                                    <div class="nim-dialog--date _im_dialog_date">{$lastMsg->getSendTimeHumanized()}</div>
                                </div>

                                <div class="_im_dialog_title" title="{$recipient->getCanonicalName()}">
                                    <div class="nim-dialog--name">
                                        <span class="nim-dialog--name-w _im_dialog_name_w">
                                            <a href="{$recipient->getURL()}">{$recipient->getCanonicalName()}</a>
                                        </span>
                                    </div>
                                    <div class="nim-dialog--text-preview">
                                        <span class="nim-dialog--preview _dialog_body" tabindex="0">
                                            <span class="nim-dialog--who">
                                                <div class="im-prebody">
                                                    <img alt="" src="{if str_contains($lastMsg->getSender()->getAvatarURL('miniscule'), 'camera_200.png')}/themepack/modern_mobile/{$theme->getVersion()}/resource/camera_200.png{else}{$lastMsg->getSender()->getAvatarURL('miniscule')}{/if}">
                                                </div>
                                            </span>
                                            <span class="nim-dialog--inner-text">{$lastMsg->getText()|noescape}</span>
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </li>
				{/foreach}
                </ul>
			{else}
				<center class="im_none">{_no_messages}</center>
			{/if}
        </div>
        <div style="margin-top: 3px;">
            {include "../components/paginator.xml", conf => $paginatorConf}
        </div>
	</div>
{/block}