<!DOCTYPE html>
<html>
<head>
    <title>Mobile Menu Test - OpenVK Modern Mobile Theme</title>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
    
    <!-- Include required dependencies -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://code.jquery.com/ui/1.13.2/jquery-ui.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/umbrellajs@3.3.0/umbrella.min.js"></script>
    
    <!-- Include theme CSS -->
    <link rel="stylesheet" href="stylesheet.css" />
    <link rel="stylesheet" href="stylesheet-mobile.css" />
    
    <style>
        /* Basic test page styling */
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f7f8fa;
        }
        
        .test-container {
            max-width: 400px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .test-button {
            background: #5181b8;
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 6px;
            font-size: 16px;
            cursor: pointer;
            width: 100%;
            margin: 10px 0;
        }
        
        .test-button:hover {
            background: #4a76a8;
        }
        
        .instructions {
            background: #e8f4fd;
            padding: 15px;
            border-radius: 6px;
            margin: 20px 0;
            border-left: 4px solid #5181b8;
        }
        
        /* Mock navigation for testing */
        .navigation {
            display: none; /* Hidden in test, but available for the script */
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>Mobile Menu Test</h1>
        
        <div class="instructions">
            <h3>Test Instructions:</h3>
            <ol>
                <li>Click the "Open Mobile Menu" button below</li>
                <li>The mobile menu should open in a full-screen modal</li>
                <li>You should see navigation links in the modal</li>
                <li>Click outside the modal or press Escape to close</li>
                <li>Try the keyboard shortcut: Ctrl+Shift+M</li>
            </ol>
        </div>
        
        <button class="test-button" onclick="window.openMenu()">
            📱 Open Mobile Menu
        </button>
        
        <button class="test-button" onclick="testMenuFunction()">
            🧪 Test Menu Function
        </button>
        
        <div id="test-results"></div>
    </div>

    <!-- Mock navigation content for testing -->
    <div class="navigation" style="display: none;">
        <a href="/user123" class="link">My Page</a>
        <a href="/feed" class="link">My Feed</a>
        <a href="/im" class="link">My Messages
            <object type="internal/link">
                +<b>3</b>
            </object>
        </a>
        <a href="/friends123" class="link">My Friends
            <object type="internal/link">
                <a href="/friends123?act=incoming" class="linkunderline">
                   +<b>2</b>
                </a>
            </object>
        </a>
        <a href="/groups123" class="link">My Groups</a>
        <a href="/albums123" class="link">My Photos</a>
        <a href="/audios123" class="link">My Audios</a>
        <a href="/videos123" class="link">My Videos</a>
        <a href="/apps?act=installed" class="link">Apps</a>
        <a href="/notes123" class="link">My Notes</a>
        
        <div class="menu_divider"></div>
        <a href="/docs" class="link">My Documents</a>
        <a href="/fave" class="link">Bookmarks</a>
        
        <div class="menu_divider"></div>
        <a href="/admin" class="link">Admin</a>
        <a href="/support/tickets" class="link">Helpdesk</a>
        
        <div id="news">
            <b>News</b>
            <hr size="1">
            <text>Welcome to the mobile menu test! This is a sample news item...</text>
            <br><br>
            <a href="/news">Read more</a>
        </div>
        
        <div class="left_menu_nav_wrap">
            <a href="/blog">Blog</a>
            <a href="/support">Help</a>
            <a href="/terms">Rules</a>
            <a href="/about">About</a>
        </div>
    </div>

    <!-- Include required scripts -->
    <script src="res/temp/messagebox.js"></script>
    <script src="res/temp/mobile_menu.js"></script>
    
    <script>
        // Test function to verify the mobile menu works
        function testMenuFunction() {
            const results = document.getElementById('test-results');
            results.innerHTML = '<h3>Test Results:</h3>';
            
            // Test 1: Check if window.openMenu exists
            if (typeof window.openMenu === 'function') {
                results.innerHTML += '<p>✅ window.openMenu function exists</p>';
            } else {
                results.innerHTML += '<p>❌ window.openMenu function not found</p>';
                return;
            }
            
            // Test 2: Check if navigation element exists
            const navigation = document.querySelector('.navigation');
            if (navigation) {
                results.innerHTML += '<p>✅ Navigation element found</p>';
            } else {
                results.innerHTML += '<p>❌ Navigation element not found</p>';
                return;
            }
            
            // Test 3: Check if CMessageBox is available
            if (typeof CMessageBox === 'function') {
                results.innerHTML += '<p>✅ CMessageBox class available</p>';
            } else {
                results.innerHTML += '<p>❌ CMessageBox class not found</p>';
                return;
            }
            
            // Test 4: Try to open the menu
            try {
                const menu = window.openMenu();
                if (menu) {
                    results.innerHTML += '<p>✅ Mobile menu opened successfully</p>';
                    // Close it after a moment for testing
                    setTimeout(() => {
                        menu.close();
                        results.innerHTML += '<p>✅ Mobile menu closed successfully</p>';
                    }, 2000);
                } else {
                    results.innerHTML += '<p>❌ Mobile menu failed to open</p>';
                }
            } catch (error) {
                results.innerHTML += `<p>❌ Error opening mobile menu: ${error.message}</p>';
            }
        }
        
        // Initialize messagebox stack if not already done
        if (!window.messagebox_stack) {
            window.messagebox_stack = [];
        }
        
        console.log('Mobile Menu Test Page Loaded');
        console.log('Available functions:', {
            openMenu: typeof window.openMenu,
            CMessageBox: typeof CMessageBox,
            messagebox_stack: Array.isArray(window.messagebox_stack)
        });
    </script>
</body>
</html>
